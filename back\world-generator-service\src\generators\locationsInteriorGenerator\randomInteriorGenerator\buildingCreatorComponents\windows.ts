import { TransferLocation } from "../../../../shared/types/Location";
import { LocationDecorations } from "../../../../shared/enums";
import { isThreeWallJunction } from "./doors";

/**
 * Добавляет окна в здание - по периметру каждые 2-4 стены
 */
export async function addBuildingWindows(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  rng: () => number
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.WINDOW]) {
    location.decorations[LocationDecorations.WINDOW] = [];
  }

  const windows = location.decorations[LocationDecorations.WINDOW];
  const walls = location.decorations[LocationDecorations.WALL] || [];
  const doors = location.decorations[LocationDecorations.DOOR] || [];

  // Собираем стены по сторонам здания
  const wallsBySide = {
    top: [],    // верхняя стена
    bottom: [], // нижняя стена
    left: [],   // левая стена
    right: []   // правая стена
  };

  for (const wall of walls) {
    const [x, y] = wall;

    // Определяем к какой стороне относится стена
    if (y === startY && x > startX && x < startX + size - 1) {
      wallsBySide.top.push(wall);
    } else if (y === startY + size - 1 && x > startX && x < startX + size - 1) {
      wallsBySide.bottom.push(wall);
    } else if (x === startX && y > startY && y < startY + size - 1) {
      wallsBySide.left.push(wall);
    } else if (x === startX + size - 1 && y > startY && y < startY + size - 1) {
      wallsBySide.right.push(wall);
    }
  }

  // Размещаем окна на каждой стороне
  for (const sideWalls of Object.values(wallsBySide)) {
    if (sideWalls.length === 0) continue;

    // Интервал между окнами: каждые 2-4 стены
    const interval = Math.floor(rng() * 3) + 3; // 2, 3 или 4

    for (let i = interval - 1; i < sideWalls.length; i += interval) {
      const wall = sideWalls[i];
      const [x, y] = wall;

      // Проверяем, что это не стык трех стен
      const isJunction = isThreeWallJunction(x, y, walls);
      if (isJunction) continue;

      // Проверяем, что рядом нет двери (минимум 2 клетки расстояния)
      const nearDoor = doors.some(door =>
        Math.abs(door[0] - x) <= 2 && Math.abs(door[1] - y) <= 2
      );
      if (nearDoor) continue;

      // Добавляем окно
      windows.push(wall);

      // Удаляем стену в позиции окна
      const wallIndex = walls.findIndex(w => w[0] === x && w[1] === y);
      if (wallIndex !== -1) {
        walls.splice(wallIndex, 1);
      }
    }
  }
}
