import { TransferLocation, Point } from "../../../../shared/types/Location";
import { LocationDecorations, DecorationZoneType } from "../../../../shared/enums";

/**
 * Добавляет двери в здание - НОВАЯ СИСТЕМА
 * Анализирует всю сетку здания целиком и размещает двери за один проход
 */
export async function addBuildingDoors(
  location: TransferLocation,
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): Promise<void> {
  if (!location.decorations) {
    location.decorations = {};
  }

  if (!location.decorations[LocationDecorations.DOOR]) {
    location.decorations[LocationDecorations.DOOR] = [];
  }

  const doors = location.decorations[LocationDecorations.DOOR];
  const walls = location.decorations[LocationDecorations.WALL] || [];

  // Получаем все комнаты из зон декораций
  const rooms = findRoomsFromDecorationZones(location);

  // Создаем сетку здания для анализа
  const buildingGrid = createBuildingGrid(startX, startY, size, walls, rooms);

  // Размещаем внешние двери
  const externalDoors = placeExternalDoors(buildingGrid, startX, startY, size, doorCount, rng);

  // Размещаем внутренние двери между комнатами
  const internalDoors = placeInternalDoors(buildingGrid, rooms, rng);

  // Добавляем все двери в локацию и удаляем соответствующие стены
  const allDoors = [...externalDoors, ...internalDoors];
  for (const doorPosition of allDoors) {
    doors.push(doorPosition);

    // Удаляем стену в позиции двери
    const wallIndex = walls.findIndex(wall =>
      wall[0] === doorPosition[0] && wall[1] === doorPosition[1]
    );
    if (wallIndex !== -1) {
      walls.splice(wallIndex, 1);
    }
  }
}

// Интерфейсы для новой системы
interface BuildingCell {
  x: number;
  y: number;
  isWall: boolean;
  isExternalWall: boolean;
  roomId: number | null; // ID комнаты, к которой принадлежит клетка
  wallConnections: number; // количество соседних стен
}



/**
 * Создает сетку здания для анализа размещения дверей
 */
function createBuildingGrid(
  startX: number,
  startY: number,
  size: number,
  walls: Point[],
  rooms: Array<{x: number, y: number, width: number, height: number}>
): BuildingCell[][] {
  // Создаем двумерную сетку
  const grid: BuildingCell[][] = [];

  for (let y = startY; y < startY + size; y++) {
    const row: BuildingCell[] = [];
    for (let x = startX; x < startX + size; x++) {
      // Проверяем, есть ли стена в этой позиции
      const isWall = walls.some(wall => wall[0] === x && wall[1] === y);

      // Проверяем, является ли это внешней стеной здания
      const isExternalWall = (x === startX || x === startX + size - 1 ||
                             y === startY || y === startY + size - 1);

      // Определяем к какой комнате принадлежит клетка
      let roomId: number | null = null;
      for (let i = 0; i < rooms.length; i++) {
        const room = rooms[i];
        if (x >= room.x && x < room.x + room.width &&
            y >= room.y && y < room.y + room.height) {
          roomId = i;
          break;
        }
      }

      row.push({
        x,
        y,
        isWall,
        isExternalWall,
        roomId,
        wallConnections: 0
      });
    }
    grid.push(row);
  }

  // Подсчитываем соединения стен для каждой клетки
  for (let y = 0; y < grid.length; y++) {
    for (let x = 0; x < grid[y].length; x++) {
      const cell = grid[y][x];
      if (cell.isWall) {
        // Считаем соседние стены
        const neighbors = [
          [x - 1, y], [x + 1, y], // горизонтальные соседи
          [x, y - 1], [x, y + 1]  // вертикальные соседи
        ];

        let wallCount = 0;
        for (const [nx, ny] of neighbors) {
          if (nx >= 0 && nx < grid[0].length && ny >= 0 && ny < grid.length) {
            if (grid[ny][nx].isWall) wallCount++;
          }
        }
        cell.wallConnections = wallCount;
      }
    }
  }

  return grid;
}

/**
 * Размещает внешние двери (входы/выходы в здание)
 */
function placeExternalDoors(
  grid: BuildingCell[][],
  startX: number,
  startY: number,
  size: number,
  doorCount: number,
  rng: () => number
): Point[] {
  const possiblePositions: Point[] = [];

  // Собираем все возможные позиции для внешних дверей
  for (let y = 0; y < grid.length; y++) {
    for (let x = 0; x < grid[y].length; x++) {
      const cell = grid[y][x];

      // Должна быть внешней стеной
      if (!cell.isWall || !cell.isExternalWall) continue;

      // Исключаем углы здания
      const isCorner = (cell.x === startX || cell.x === startX + size - 1) &&
                       (cell.y === startY || cell.y === startY + size - 1);
      if (isCorner) continue;

      // Исключаем стыки с большим количеством соединений (внутренние углы)
      if (cell.wallConnections >= 3) continue;

      possiblePositions.push([cell.x, cell.y]);
    }
  }

  // Размещаем двери
  const doors: Point[] = [];
  const maxDoors = Math.min(doorCount, possiblePositions.length);

  for (let i = 0; i < maxDoors; i++) {
    const doorIndex = Math.floor(rng() * possiblePositions.length);
    const doorPosition = possiblePositions.splice(doorIndex, 1)[0];
    doors.push(doorPosition);
  }

  return doors;
}

/**
 * Размещает внутренние двери между комнатами
 */
function placeInternalDoors(
  grid: BuildingCell[][],
  rooms: Array<{x: number, y: number, width: number, height: number}>,
  rng: () => number
): Point[] {
  if (rooms.length <= 1) return []; // Нет смысла добавлять двери если комната одна или их нет

  const doors: Point[] = [];
  const usedWalls = new Set<string>();

  // Для каждой комнаты ищем места для дверей
  for (let roomIndex = 0; roomIndex < rooms.length; roomIndex++) {
    const room = rooms[roomIndex];

    // Ищем стены комнаты, которые граничат с другими комнатами
    const possibleDoorPositions: Array<{point: Point, direction: string, neighborRoomId: number}> = [];

    // Проверяем периметр комнаты
    for (let x = room.x; x < room.x + room.width; x++) {
      for (let y = room.y; y < room.y + room.height; y++) {
        // Проверяем только границы комнаты
        const isPerimeter = (x === room.x || x === room.x + room.width - 1 ||
                            y === room.y || y === room.y + room.height - 1);
        if (!isPerimeter) continue;

        // Исключаем углы
        const isCorner = (x === room.x || x === room.x + room.width - 1) &&
                         (y === room.y || y === room.y + room.height - 1);
        if (isCorner) continue;

        const gridX = x - grid[0][0].x;
        const gridY = y - grid[0][0].y;

        if (gridY < 0 || gridY >= grid.length || gridX < 0 || gridX >= grid[0].length) continue;

        const cell = grid[gridY][gridX];

        // Должна быть стеной, но не внешней стеной здания
        if (!cell.isWall || cell.isExternalWall) continue;

        // Исключаем стыки с большим количеством соединений
        if (cell.wallConnections >= 3) continue;

        // Проверяем, есть ли соседняя комната
        const directions = [
          {dx: -1, dy: 0, name: 'west'},
          {dx: 1, dy: 0, name: 'east'},
          {dx: 0, dy: -1, name: 'north'},
          {dx: 0, dy: 1, name: 'south'}
        ];

        for (const dir of directions) {
          const neighborX = gridX + dir.dx;
          const neighborY = gridY + dir.dy;

          if (neighborY < 0 || neighborY >= grid.length || neighborX < 0 || neighborX >= grid[0].length) continue;

          const neighborCell = grid[neighborY][neighborX];

          // Если соседняя клетка принадлежит другой комнате
          if (neighborCell.roomId !== null && neighborCell.roomId !== roomIndex && !neighborCell.isWall) {
            possibleDoorPositions.push({
              point: [cell.x, cell.y],
              direction: dir.name,
              neighborRoomId: neighborCell.roomId
            });
          }
        }
      }
    }

    // Размещаем одну дверь для комнаты (если возможно)
    if (possibleDoorPositions.length > 0) {
      // Фильтруем уже использованные позиции
      const availablePositions = possibleDoorPositions.filter(pos => {
        const key = `${pos.point[0]},${pos.point[1]}`;
        return !usedWalls.has(key);
      });

      if (availablePositions.length > 0) {
        const doorIndex = Math.floor(rng() * availablePositions.length);
        const selectedDoor = availablePositions[doorIndex];

        doors.push(selectedDoor.point);
        usedWalls.add(`${selectedDoor.point[0]},${selectedDoor.point[1]}`);
      }
    }
  }

  return doors;
}

/**
 * Находит комнаты из зон декораций
 */
function findRoomsFromDecorationZones(location: TransferLocation): Array<{x: number, y: number, width: number, height: number}> {
  const rooms: Array<{x: number, y: number, width: number, height: number}> = [];

  if (!location.decorationZoneType) return rooms;

  // Группируем зоны по ID
  const zoneGroups = new Map<number, Point[]>();

  for (const zoneType in location.decorationZoneType) {
    const zones = location.decorationZoneType[zoneType as DecorationZoneType];
    if (!zones) continue;

    for (const zone of zones) {
      const [x, y, zoneId] = zone;
      if (!zoneGroups.has(zoneId)) {
        zoneGroups.set(zoneId, []);
      }
      zoneGroups.get(zoneId)!.push([x, y]);
    }
  }

  // Для каждой группы зон определяем границы комнаты
  for (const [, points] of zoneGroups) {
    if (points.length === 0) continue;

    let minX = points[0][0], maxX = points[0][0];
    let minY = points[0][1], maxY = points[0][1];

    for (const [x, y] of points) {
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);
    }

    // Расширяем границы на 1 клетку для учета стен
    rooms.push({
      x: minX - 1,
      y: minY - 1,
      width: maxX - minX + 3,
      height: maxY - minY + 3
    });
  }

  return rooms;
}

/**
 * Находит стены комнаты (только внутренние стены, не внешние стены здания)
 */
function findRoomWalls(
  room: {x: number, y: number, width: number, height: number},
  allWalls: Point[],
  buildingStartX: number,
  buildingStartY: number,
  buildingSize: number
): Point[] {
  const roomWalls: Point[] = [];

  for (const wall of allWalls) {
    const [x, y] = wall;

    // Проверяем, что стена принадлежит периметру комнаты
    const isRoomPerimeter = (
      (x === room.x || x === room.x + room.width - 1) && y >= room.y && y < room.y + room.height
    ) || (
      (y === room.y || y === room.y + room.height - 1) && x >= room.x && x < room.x + room.width
    );

    if (!isRoomPerimeter) continue;

    // Исключаем внешние стены здания
    const isBuildingExternalWall = (
      x === buildingStartX || x === buildingStartX + buildingSize - 1 ||
      y === buildingStartY || y === buildingStartY + buildingSize - 1
    );

    if (isBuildingExternalWall) continue;

    // Исключаем углы комнаты
    const isRoomCorner = (
      (x === room.x || x === room.x + room.width - 1) &&
      (y === room.y || y === room.y + room.height - 1)
    );

    if (isRoomCorner) continue;

    roomWalls.push([x, y]);
  }

  return roomWalls;
}